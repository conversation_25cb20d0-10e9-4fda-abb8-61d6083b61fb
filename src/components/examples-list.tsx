'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, Loader2 } from 'lucide-react';
import { Translate } from '@/components/ui/translate';
import { useWordExamples } from '@/hooks/use-word-examples';
import { Language } from '@prisma/client';

interface Example {
	id?: string;
	EN: string;
	VI: string;
	created_at?: Date;
	updated_at?: Date;
}

interface Definition {
	id: string;
	examples: Example[];
}

interface ExamplesListProps {
	wordId: string;
	definition: Definition;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: () => void;
	generatingExamples?: boolean;
}

function ExamplesListComponent({
	wordId,
	definition,
	sourceLanguage,
	targetLanguage,
	generatingExamples = false,
}: Omit<ExamplesListProps, 'onGenerateExamples'>) {
	const { getExampleState, loadMoreExamples, initializeExamples } = useWordExamples();
	const exampleState = getExampleState(definition.id);

	// Initialize examples state when component mounts or definition changes
	React.useEffect(() => {
		// Only initialize if we don't already have examples for this definition
		// or if the definition examples have changed
		const currentState = getExampleState(definition.id);
		const shouldInitialize =
			currentState.examples.length === 0 ||
			currentState.initialExamplesCount !== definition.examples.length;

		if (shouldInitialize) {
			initializeExamples(definition.id, definition.examples);
		}
	}, [definition.id, definition.examples, initializeExamples, getExampleState]);

	// Use only the examples from the hook state to avoid duplicates
	const allExamples = React.useMemo(() => {
		return exampleState.examples;
	}, [exampleState.examples]);

	const isLoadingExamples = exampleState.loading || generatingExamples;
	const canLoadMore = wordId && exampleState.hasMore && !isLoadingExamples;

	const handleLoadMore = async () => {
		if (!wordId) return;
		await loadMoreExamples(wordId, definition.id);
	};

	return (
		<div>
			<div className="flex items-center justify-between mb-1.5">
				<p className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
					<Translate text="words.examples" />:
					{isLoadingExamples && (
						<span className="flex items-center gap-1 text-xs text-muted-foreground/70">
							<Loader2 className="h-3 w-3 animate-spin" />
							<Translate text="words.loading" />
						</span>
					)}
				</p>
			</div>

			<div className="space-y-2">
				{allExamples.length > 0 ? (
					<>
						{allExamples.map((example, exIndex) => (
							<div
								key={
									example.id ||
									`example-${exIndex}-${example.EN?.slice(
										0,
										20
									)}-${example.VI?.slice(0, 20)}`
								}
								className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
							>
								<div className="text-sm">
									<div className="font-medium text-foreground mb-1">
										{sourceLanguage === Language.EN ? example.EN : example.VI}
									</div>
									<div className="text-muted-foreground">
										{targetLanguage === Language.VI ? example.VI : example.EN}
									</div>
								</div>
							</div>
						))}
					</>
				) : (
					<p className="text-sm text-muted-foreground italic">
						<Translate text="words.no_examples_available" />
					</p>
				)}

				{/* Load More Button */}
				{canLoadMore && (
					<div className="flex justify-center mt-3">
						<Button
							variant="outline"
							size="sm"
							onClick={handleLoadMore}
							disabled={isLoadingExamples}
							className="h-8 px-3 text-xs"
						>
							{isLoadingExamples ? (
								<>
									<Loader2 className="h-3 w-3 animate-spin mr-1" />
									<Translate text="words.loading_examples" />
								</>
							) : (
								<>
									<ChevronDown className="h-3 w-3 mr-1" />
									<Translate text="words.load_more_examples" />
								</>
							)}
						</Button>
					</div>
				)}

				{/* Loading Skeleton for new examples */}
				{isLoadingExamples && (
					<div className="space-y-2 mt-2">
						{[1, 2, 3].map((i) => (
							<div key={`skeleton-${i}`} className="animate-pulse">
								<div className="pl-3 border-l-2 border-secondary/30 py-1">
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded mb-1 w-full"></div>
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
								</div>
							</div>
						))}
					</div>
				)}

				{/* Error Message */}
				{exampleState.error && (
					<div className="text-xs text-destructive text-center mt-2">
						{exampleState.error}
					</div>
				)}
			</div>
		</div>
	);
}

export { ExamplesListComponent as ExamplesList };
