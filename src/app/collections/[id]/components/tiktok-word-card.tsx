'use client';

import { But<PERSON>, Translate } from '@/components/ui';
import { WordNetInfo, WordNetSummary } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { Eye, EyeOff, Volume2, ChevronDown, ChevronUp } from 'lucide-react';
import { memo, useCallback, useState, useEffect } from 'react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import { motion, AnimatePresence } from 'framer-motion';

interface TikTokWordCardProps {
	word: WordDetail;
	sourceLanguage: Language;
	targetLanguage: Language;
	onWordViewed?: (wordId: string) => void;
	showSourceLanguage?: boolean;
	onToggleTargetLanguage?: () => void;
	isActive?: boolean;
}

function TikTokWordCardComponent({
	word,
	sourceLanguage,
	targetLanguage,
	onWordViewed,
	showSourceLanguage = false,
	onToggleTargetLanguage,
	isActive = false,
}: TikTokWordCardProps) {
	const [isExpanded, setIsExpanded] = useState(false);
	const [hasBeenViewed, setHasBeenViewed] = useState(false);

	// Use intersection observer to detect when word is viewed
	const { elementRef, hasTriggered } = useIntersectionObserver({
		threshold: 0.6, // 60% of the card must be visible
		triggerOnce: true,
	});

	// Mark word as viewed when it comes into view
	useEffect(() => {
		if (hasTriggered && !hasBeenViewed && onWordViewed) {
			setHasBeenViewed(true);
			onWordViewed(word.id);
		}
	}, [hasTriggered, hasBeenViewed, onWordViewed, word.id]);

	const handleToggleExpand = useCallback(() => {
		setIsExpanded((prev) => !prev);
	}, []);

	const handleToggleTargetLanguage = useCallback(() => {
		if (onToggleTargetLanguage) {
			onToggleTargetLanguage();
		}
	}, [onToggleTargetLanguage]);

	const handlePlayAudio = useCallback(() => {
		// Text-to-speech functionality
		if ('speechSynthesis' in window) {
			const utterance = new SpeechSynthesisUtterance(word.term);
			utterance.lang = sourceLanguage === 'EN' ? 'en-US' : 'vi-VN';
			speechSynthesis.speak(utterance);
		}
	}, [word.term, sourceLanguage]);

	return (
		<motion.div
			ref={elementRef}
			className="relative h-screen w-full flex items-center justify-center snap-start snap-always px-6 py-8 pb-24 md:pb-8"
			initial={{ opacity: 0, scale: 0.9 }}
			animate={{ opacity: 1, scale: 1 }}
			transition={{ duration: 0.3, ease: 'easeOut' }}
		>
			{/* Dynamic background gradient */}
			<div
				className={cn(
					'absolute inset-0 transition-all duration-500',
					hasBeenViewed
						? 'bg-gradient-to-br from-green-900/20 via-emerald-900/20 to-teal-900/20'
						: 'bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-pink-900/20'
				)}
			/>

			{/* Main content - Full screen width */}
			<motion.div
				className="relative z-10 w-full max-h-[85vh] md:max-h-[90vh] overflow-y-auto scrollbar-hide"
				initial={{ y: 30, opacity: 0 }}
				animate={{ y: 0, opacity: 1 }}
				transition={{ delay: 0.1, duration: 0.4 }}
			>
				{/* Word Header */}
				<motion.div
					className="text-center mb-4 md:mb-6"
					initial={{ scale: 0.9, opacity: 0 }}
					animate={{ scale: 1, opacity: 1 }}
					transition={{ delay: 0.2, duration: 0.3 }}
				>
					<h1
						className={cn(
							'text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r mb-2 md:mb-3 transition-all duration-500 break-words px-2',
							hasBeenViewed
								? 'from-green-600 to-emerald-600'
								: 'from-purple-600 to-pink-600'
						)}
					>
						{word.term}
					</h1>

					{/* WordNet Summary */}
					<div className="flex justify-center mb-2 md:mb-3">
						<WordNetSummary wordNetData={word.WordNetData} />
					</div>

					{/* Floating particles effect */}
					{hasBeenViewed && (
						<div className="absolute inset-0 pointer-events-none">
							{[...Array(3)].map((_, i) => (
								<motion.div
									key={i}
									className="absolute w-2 h-2 bg-green-400 rounded-full"
									initial={{
										x: Math.random() * 300,
										y: Math.random() * 100,
										opacity: 0,
									}}
									animate={{
										y: [0, -15, -30],
										opacity: [0, 1, 0],
										scale: [0.5, 1, 0.5],
									}}
									transition={{
										duration: 2,
										delay: i * 0.3,
										repeat: Infinity,
										repeatDelay: 4,
									}}
								/>
							))}
						</div>
					)}
				</motion.div>

				{/* Definitions section - Full width */}
				<div className="space-y-3 md:space-y-4 w-full">
					{word.definitions && word.definitions.length > 0 ? (
						word.definitions.map((definition, index) => (
							<motion.div
								key={index}
								className="p-3 md:p-4 rounded-xl border border-white/20 bg-white/10 backdrop-blur-sm"
								initial={{ opacity: 0, y: 10 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.3 + index * 0.05 }}
							>
								{/* Parts of speech */}
								{definition.pos && definition.pos.length > 0 && (
									<p className="text-xs font-semibold uppercase tracking-wider text-white/80 mb-2">
										{definition.pos.join(', ')}
									</p>
								)}

								{/* IPA pronunciation */}
								{definition.ipa && (
									<div className="flex items-center gap-2 mb-3">
										<span className="text-sm text-white/70 italic">
											/{definition.ipa}/
										</span>
										<Button
											variant="ghost"
											size="sm"
											onClick={handlePlayAudio}
											className="rounded-full p-1 h-auto text-white hover:bg-white/20"
										>
											<Volume2 className="h-3 w-3" />
										</Button>
									</div>
								)}

								{/* Explanations section */}
								{definition.explains && definition.explains.length > 0 && (
									<div className="mb-4">
										{definition.explains.map((explain, expIndex) => (
											<div
												key={expIndex}
												className="mb-3 last:mb-0 pl-3 border-l-2 border-white/30"
											>
												<p className="text-xs font-medium text-white/60 mb-1">
													<Translate
														text={getTranslationKeyOfLanguage(
															targetLanguage
														)}
													/>
													:
												</p>
												<p className="mb-2 text-sm text-white/95 leading-relaxed">
													{explain[targetLanguage] || (
														<span className="italic opacity-70">
															<Translate text="words.explanation_not_provided" />
														</span>
													)}
												</p>

												{/* Source language explanation */}
												<p className="text-xs font-medium text-white/60 mb-1">
													<Translate
														text={getTranslationKeyOfLanguage(
															sourceLanguage
														)}
													/>
													:
												</p>
												<AnimatePresence>
													{showSourceLanguage ? (
														<motion.p
															initial={{ opacity: 0, height: 0 }}
															animate={{ opacity: 1, height: 'auto' }}
															exit={{ opacity: 0, height: 0 }}
															className="text-sm text-white/90 leading-relaxed"
														>
															{explain[sourceLanguage] || (
																<span className="italic opacity-70">
																	<Translate text="words.translation_not_provided" />
																</span>
															)}
														</motion.p>
													) : (
														<motion.p
															initial={{ opacity: 0 }}
															animate={{ opacity: 1 }}
															className="text-sm text-white/50 italic"
														>
															<Translate text="collections.hidden" />
														</motion.p>
													)}
												</AnimatePresence>
											</div>
										))}
									</div>
								)}

								{/* Examples section */}
								{definition.examples && definition.examples.length > 0 && (
									<div>
										<p className="text-sm font-semibold text-white/80 mb-2">
											<Translate text="words.examples" />:
										</p>
										{definition.examples.map((example, exIndex) => (
											<div
												key={exIndex}
												className="mb-3 last:mb-0 pl-3 border-l-2 border-blue-400/30"
											>
												<p className="text-xs font-medium text-white/60 mb-1">
													<Translate
														text={getTranslationKeyOfLanguage(
															targetLanguage
														)}
													/>
													:
												</p>
												<p className="mb-2 text-sm text-white/95 leading-relaxed italic">
													&ldquo;
													{example[targetLanguage] || (
														<span className="not-italic opacity-70">
															<Translate text="words.example_not_provided" />
														</span>
													)}
													&rdquo;
												</p>

												<p className="text-xs font-medium text-white/60 mb-1">
													<Translate
														text={getTranslationKeyOfLanguage(
															sourceLanguage
														)}
													/>
													:
												</p>
												<AnimatePresence>
													{showSourceLanguage ? (
														<motion.p
															initial={{ opacity: 0, height: 0 }}
															animate={{ opacity: 1, height: 'auto' }}
															exit={{ opacity: 0, height: 0 }}
															className="text-sm text-white/90 leading-relaxed italic"
														>
															&ldquo;
															{example[sourceLanguage] || (
																<span className="not-italic opacity-70">
																	<Translate text="words.translation_not_provided" />
																</span>
															)}
															&rdquo;
														</motion.p>
													) : (
														<motion.p
															initial={{ opacity: 0 }}
															animate={{ opacity: 1 }}
															className="text-sm text-white/50 italic"
														>
															&ldquo;
															<Translate text="collections.hidden" />
															&rdquo;
														</motion.p>
													)}
												</AnimatePresence>
											</div>
										))}
									</div>
								)}
							</motion.div>
						))
					) : (
						<motion.p
							className="p-6 text-lg text-white/70 italic text-center"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ delay: 0.3 }}
						>
							<Translate text="words.no_definitions_available" />
						</motion.p>
					)}

					{/* WordNet Information */}
					<AnimatePresence>
						{isExpanded && word.WordNetData && (
							<motion.div
								initial={{ opacity: 0, height: 0 }}
								animate={{ opacity: 1, height: 'auto' }}
								exit={{ opacity: 0, height: 0 }}
								className="p-4 rounded-xl border border-white/20 bg-white/5 backdrop-blur-sm"
							>
								<WordNetInfo wordNetData={word.WordNetData} term={word.term} />
							</motion.div>
						)}
					</AnimatePresence>

					{/* Action buttons at the end of content */}
					<motion.div
						className="flex flex-col gap-3 mt-6 mb-4 md:mb-0"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.5 }}
					>
						{/* Show/Hide Source Language Button */}
						<Button
							onClick={handleToggleTargetLanguage}
							variant="ghost"
							className={cn(
								'w-full py-3 px-4 rounded-xl border border-white/20 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-all duration-200 text-sm md:text-base',
								showSourceLanguage
									? 'border-green-400/50 bg-green-900/20'
									: 'border-orange-400/50 bg-orange-900/20'
							)}
						>
							{showSourceLanguage ? (
								<>
									<EyeOff className="h-4 w-4 mr-2 flex-shrink-0" />
									<span className="truncate">
										<Translate text="words.hide" />{' '}
										<Translate
											text={getTranslationKeyOfLanguage(sourceLanguage)}
										/>
									</span>
								</>
							) : (
								<>
									<Eye className="h-4 w-4 mr-2 flex-shrink-0" />
									<span className="truncate">
										<Translate text="words.show" />{' '}
										<Translate
											text={getTranslationKeyOfLanguage(sourceLanguage)}
										/>
									</span>
								</>
							)}
						</Button>

						{/* Expand/Collapse WordNet Button */}
						{word.WordNetData && (
							<Button
								onClick={handleToggleExpand}
								variant="ghost"
								className="w-full py-3 px-4 rounded-xl border border-white/20 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-all duration-200 text-sm md:text-base"
							>
								{isExpanded ? (
									<>
										<ChevronUp className="h-4 w-4 mr-2 flex-shrink-0" />
										<span className="truncate">
											<Translate text="wordnet_lookup.collapse_details" />
										</span>
									</>
								) : (
									<>
										<ChevronDown className="h-4 w-4 mr-2 flex-shrink-0" />
										<span className="truncate">
											<Translate text="wordnet_lookup.expand_details" />
										</span>
									</>
								)}
							</Button>
						)}
					</motion.div>
				</div>
			</motion.div>
		</motion.div>
	);
}

export const TikTokWordCard = memo(TikTokWordCardComponent);
