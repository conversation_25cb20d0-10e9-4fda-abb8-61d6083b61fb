import { describe, it, expect, vi } from 'vitest';

// Mock Example type
interface Example {
	id: string;
	EN: string;
	VI: string;
	created_at: Date;
	updated_at: Date;
}

// Mock the sorting logic from examples-list.tsx
function combineAndSortExamples(initialExamples: Example[], loadedExamples: Example[]): Example[] {
	const seenIds = new Set<string>();
	const seenContents = new Set<string>();
	const combined: Example[] = [];

	// Helper function to normalize content for comparison
	const normalizeContent = (example: Example) =>
		`${example.EN.toLowerCase().trim()}|${example.VI.toLowerCase().trim()}`;

	// Combine all examples from both sources first
	const allSources = [...initialExamples, ...loadedExamples];

	// Add all examples, skipping duplicates
	for (const example of allSources) {
		const contentKey = normalizeContent(example);

		if (example.id && !seenIds.has(example.id) && !seenContents.has(contentKey)) {
			seenIds.add(example.id);
			seenContents.add(contentKey);
			combined.push(example);
		} else if (!example.id && !seenContents.has(contentKey)) {
			// If no ID, check content only
			seenContents.add(contentKey);
			combined.push(example);
		}
	}

	// Sort by created_at in descending order (newest first) to maintain consistent chronological order
	return combined.sort((a, b) => {
		// Handle cases where created_at might be missing (shouldn't happen but safety first)
		const aTime = a.created_at ? new Date(a.created_at).getTime() : 0;
		const bTime = b.created_at ? new Date(b.created_at).getTime() : 0;
		return bTime - aTime; // Descending order (newest first)
	});
}

describe('Examples Sorting by created_at', () => {
	const now = new Date();
	const hour1 = new Date(now.getTime() - 1 * 60 * 60 * 1000); // 1 hour ago
	const hour2 = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
	const hour3 = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 hours ago

	const initialExamples: Example[] = [
		{
			id: 'ex-1',
			EN: 'Hello world',
			VI: 'Xin chào thế giới',
			created_at: hour2, // 2 hours ago
			updated_at: hour2,
		},
		{
			id: 'ex-2',
			EN: 'Hello there',
			VI: 'Xin chào',
			created_at: hour3, // 3 hours ago (oldest)
			updated_at: hour3,
		},
	];

	const loadedExamples: Example[] = [
		{
			id: 'ex-3',
			EN: 'Hello friend',
			VI: 'Xin chào bạn',
			created_at: now, // Most recent
			updated_at: now,
		},
		{
			id: 'ex-4',
			EN: 'Say hello',
			VI: 'Nói xin chào',
			created_at: hour1, // 1 hour ago
			updated_at: hour1,
		},
	];

	it('should sort combined examples by created_at in descending order (newest first)', () => {
		const result = combineAndSortExamples(initialExamples, loadedExamples);

		// Should be sorted: now -> hour1 -> hour2 -> hour3
		expect(result).toHaveLength(4);
		expect(result[0].id).toBe('ex-3'); // now (most recent)
		expect(result[1].id).toBe('ex-4'); // hour1
		expect(result[2].id).toBe('ex-1'); // hour2
		expect(result[3].id).toBe('ex-2'); // hour3 (oldest)

		// Verify actual timestamps are in correct order
		for (let i = 0; i < result.length - 1; i++) {
			const currentTime = new Date(result[i].created_at).getTime();
			const nextTime = new Date(result[i + 1].created_at).getTime();
			expect(currentTime).toBeGreaterThanOrEqual(nextTime);
		}
	});

	it('should handle duplicate examples correctly while maintaining sort order', () => {
		const duplicateExamples: Example[] = [
			...loadedExamples,
			{
				id: 'ex-1', // Duplicate ID
				EN: 'Hello world',
				VI: 'Xin chào thế giới',
				created_at: hour2,
				updated_at: hour2,
			},
			{
				id: 'ex-5',
				EN: 'Hello there', // Duplicate content
				VI: 'Xin chào',
				created_at: now, // Different timestamp but same content
				updated_at: now,
			},
		];

		const result = combineAndSortExamples(initialExamples, duplicateExamples);

		// Should still have only 4 unique examples
		expect(result).toHaveLength(4);

		// Should be sorted by created_at
		expect(result[0].id).toBe('ex-3'); // now (most recent)
		expect(result[1].id).toBe('ex-4'); // hour1
		expect(result[2].id).toBe('ex-1'); // hour2
		expect(result[3].id).toBe('ex-2'); // hour3 (oldest)
	});

	it('should handle examples without created_at gracefully (fallback to timestamp 0)', () => {
		const examplesWithMissingDates: Example[] = [
			{
				id: 'ex-5',
				EN: 'No date example',
				VI: 'Ví dụ không có ngày',
				created_at: null as any, // Missing date
				updated_at: now,
			},
		];

		const result = combineAndSortExamples(initialExamples, examplesWithMissingDates);

		// Examples with missing dates should be sorted last (timestamp 0)
		expect(result).toHaveLength(3);
		expect(result[0].id).toBe('ex-1'); // hour2
		expect(result[1].id).toBe('ex-2'); // hour3
		expect(result[2].id).toBe('ex-5'); // Missing date (sorted last)
	});
});
